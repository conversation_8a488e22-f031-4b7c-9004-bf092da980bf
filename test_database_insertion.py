#!/usr/bin/env python3
"""
Test script to verify database insertion of connection matches
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from graph.sqlite_client import SQLiteConnection
from services.cross_indexing.core.connection_matching_service import ConnectionMatchingService
from datetime import datetime
import json

def test_database_insertion():
    """Test if the connection matches from your log are being inserted correctly"""
    
    # Your actual match data from the log
    test_matches = [
        {
            "outgoing_id": "938",
            "incoming_id": "2170", 
            "match_confidence": "high",
            "match_reason": "Exact queue name match - QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA queue publishing matches RabbitMQ consumer for the same queue",
            "connection_type": "rabbitmq"
        },
        {
            "outgoing_id": "1004",
            "incoming_id": "2170",
            "match_confidence": "high", 
            "match_reason": "Exact queue name match - QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA queue publishing matches RabbitMQ consumer for the same queue",
            "connection_type": "rabbitmq"
        },
        {
            "outgoing_id": "1004",
            "incoming_id": "2431",
            "match_confidence": "high",
            "match_reason": "Exact queue name match - QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA queue publishing matches RabbitMQ consumer for the same queue", 
            "connection_type": "rabbitmq"
        },
        {
            "outgoing_id": "938",
            "incoming_id": "2431",
            "match_confidence": "high",
            "match_reason": "Exact queue name match - QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA queue publishing matches RabbitMQ consumer for the same queue",
            "connection_type": "rabbitmq"
        }
    ]
    
    print("🔍 Testing database insertion with your actual match data...")
    
    # Initialize database connection
    db_client = SQLiteConnection()
    
    # Check if the referenced connections exist
    print("\n📋 Checking if referenced connections exist in database:")
    
    outgoing_ids = ["938", "1004"]
    incoming_ids = ["2170", "2431"]
    
    for oid in outgoing_ids:
        result = db_client.execute_query("SELECT id, description FROM outgoing_connections WHERE id = ?", (oid,))
        if result:
            print(f"✅ Outgoing connection {oid} exists: {result[0]['description'][:50]}...")
        else:
            print(f"❌ Outgoing connection {oid} NOT FOUND")
    
    for iid in incoming_ids:
        result = db_client.execute_query("SELECT id, description FROM incoming_connections WHERE id = ?", (iid,))
        if result:
            print(f"✅ Incoming connection {iid} exists: {result[0]['description'][:50]}...")
        else:
            print(f"❌ Incoming connection {iid} NOT FOUND")
    
    # Check current matches in database before insertion
    print(f"\n📊 Current matches in connection_mappings table:")
    current_matches = db_client.execute_query("SELECT COUNT(*) as count FROM connection_mappings")
    print(f"Current match count: {current_matches[0]['count'] if current_matches else 0}")
    
    # Test the insertion using the actual service
    print(f"\n🔄 Testing insertion using ConnectionMatchingService...")
    
    matching_service = ConnectionMatchingService()
    
    # Prepare the data in the format expected by the service
    matching_results = {
        "matches": test_matches,
        "total_matches": len(test_matches)
    }
    
    # Try to store the results
    try:
        storage_result = matching_service._store_matching_results(matching_results, project_id="1")
        print(f"✅ Storage result: {json.dumps(storage_result, indent=2)}")
        
        # Check matches after insertion
        print(f"\n📊 Matches in database after insertion:")
        after_matches = db_client.execute_query("SELECT COUNT(*) as count FROM connection_mappings")
        print(f"Match count after insertion: {after_matches[0]['count'] if after_matches else 0}")
        
        # Show the actual inserted matches
        recent_matches = db_client.execute_query("""
            SELECT sender_id, receiver_id, connection_type, description, match_confidence, created_at 
            FROM connection_mappings 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        if recent_matches:
            print(f"\n📋 Recent matches in database:")
            for match in recent_matches:
                print(f"  {match['sender_id']} -> {match['receiver_id']} ({match['connection_type']}) confidence: {match['match_confidence']}")
        else:
            print(f"\n❌ No matches found in database")
            
    except Exception as e:
        print(f"❌ Error during insertion: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_database_insertion()
