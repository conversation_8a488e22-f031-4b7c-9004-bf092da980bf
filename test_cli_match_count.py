#!/usr/bin/env python3
"""
Test script to verify the CLI match count display fix.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from graph.sqlite_client import SQLiteConnection

def test_cli_match_count():
    """Test the CLI match count logic."""
    
    print("🔍 Testing CLI match count logic...")
    
    # Test the same logic that the CLI uses
    try:
        db_client = SQLiteConnection()
        db_matches = db_client.execute_query("SELECT COUNT(*) as count FROM connection_mappings")
        matches_count = db_matches[0]['count'] if db_matches else 0
        db_client.close()
        
        print(f"✅ Database query successful: {matches_count} matches found")
        
        # Also show some sample matches
        db_client = SQLiteConnection()
        sample_matches = db_client.execute_query("""
            SELECT cm.id, cm.sender_id, cm.receiver_id, cm.connection_type, cm.match_confidence
            FROM connection_mappings cm
            ORDER BY cm.id DESC
            LIMIT 5
        """)
        
        if sample_matches:
            print(f"\n📝 Sample matches (latest 5):")
            for match in sample_matches:
                print(f"  ID {match['id']}: {match['sender_id']} -> {match['receiver_id']} ({match['connection_type']}, confidence: {match['match_confidence']})")
        
        db_client.close()
        
    except Exception as e:
        print(f"❌ Database query failed: {str(e)}")
        # This is the fallback logic
        matches_count = 0
        print(f"Using fallback count: {matches_count}")

if __name__ == "__main__":
    test_cli_match_count()
