#!/usr/bin/env python3
"""
Test script to verify if connection matches are being inserted into the database.
This will test with the actual match data from your log.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from graph.sqlite_client import SQLiteConnection
from services.cross_indexing.core.connection_matching_service import ConnectionMatchingService
from datetime import datetime
import json

def test_match_insertion():
    """Test inserting the matches from your log into the database."""
    
    # Your actual match data from the log
    matches_data = [
        {
            "outgoing_id": "938",
            "incoming_id": "2170",
            "match_confidence": "high",
            "match_reason": "Exact queue name match - QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA queue publishing matches RabbitMQ consumer for the same queue",
            "connection_type": "rabbitmq"
        },
        {
            "outgoing_id": "1004",
            "incoming_id": "2170",
            "match_confidence": "high",
            "match_reason": "Exact queue name match - QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA queue publishing matches RabbitMQ consumer for the same queue",
            "connection_type": "rabbitmq"
        },
        {
            "outgoing_id": "1004",
            "incoming_id": "2431",
            "match_confidence": "high",
            "match_reason": "Exact queue name match - QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA queue publishing matches RabbitMQ consumer for the same queue",
            "connection_type": "rabbitmq"
        },
        {
            "outgoing_id": "938",
            "incoming_id": "2431",
            "match_confidence": "high",
            "match_reason": "Exact queue name match - QUEUE_NAME_DOWNLOAD_CANDIDATE_DATA queue publishing matches RabbitMQ consumer for the same queue",
            "connection_type": "rabbitmq"
        }
    ]
    
    print("🔍 Testing connection match insertion...")
    
    # Initialize database connection
    db_client = SQLiteConnection()
    
    # Check if the connection IDs exist in the database
    print("\n📊 Checking if connection IDs exist in database:")
    
    outgoing_ids = ["938", "1004"]
    incoming_ids = ["2170", "2431"]
    
    for outgoing_id in outgoing_ids:
        result = db_client.execute_query(
            "SELECT id, description FROM outgoing_connections WHERE id = ?", 
            (outgoing_id,)
        )
        if result:
            print(f"✅ Outgoing connection {outgoing_id}: {result[0]['description'][:50]}...")
        else:
            print(f"❌ Outgoing connection {outgoing_id}: NOT FOUND")
    
    for incoming_id in incoming_ids:
        result = db_client.execute_query(
            "SELECT id, description FROM incoming_connections WHERE id = ?", 
            (incoming_id,)
        )
        if result:
            print(f"✅ Incoming connection {incoming_id}: {result[0]['description'][:50]}...")
        else:
            print(f"❌ Incoming connection {incoming_id}: NOT FOUND")
    
    # Check current matches in database
    print("\n📋 Current matches in connection_mappings table:")
    current_matches = db_client.execute_query(
        "SELECT COUNT(*) as count FROM connection_mappings"
    )
    print(f"Current match count: {current_matches[0]['count'] if current_matches else 0}")
    
    # Test insertion using the connection matching service
    print("\n🔄 Testing match insertion...")
    
    matching_service = ConnectionMatchingService()
    
    # Prepare match data in the format expected by the service
    processed_results = {
        "matches": matches_data,
        "total_matches": len(matches_data)
    }
    
    # Try to store the matches
    try:
        storage_result = matching_service._store_matching_results(processed_results, project_id="1")
        print(f"✅ Storage result: {json.dumps(storage_result, indent=2)}")
        
        # Check matches after insertion
        print("\n📋 Matches after insertion:")
        new_matches = db_client.execute_query(
            "SELECT COUNT(*) as count FROM connection_mappings"
        )
        new_count = new_matches[0]['count'] if new_matches else 0
        print(f"New match count: {new_count}")
        
        # Show actual matches
        if new_count > 0:
            actual_matches = db_client.execute_query("""
                SELECT cm.*, 
                       oc.description as outgoing_desc,
                       ic.description as incoming_desc
                FROM connection_mappings cm
                LEFT JOIN outgoing_connections oc ON cm.sender_id = oc.id
                LEFT JOIN incoming_connections ic ON cm.receiver_id = ic.id
                ORDER BY cm.id DESC
                LIMIT 10
            """)
            
            print("\n📝 Recent matches in database:")
            for match in actual_matches:
                print(f"  Match ID {match['id']}: {match['sender_id']} -> {match['receiver_id']}")
                print(f"    Type: {match['connection_type']}, Confidence: {match['match_confidence']}")
                print(f"    Reason: {match['description'][:80]}...")
                print()
        
    except Exception as e:
        print(f"❌ Error during insertion: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_match_insertion()
